#!/usr/bin/env python3
"""
免费简历模板数据导入脚本 v2.0
支持从CSV和Excel文件导入数据到数据库

新功能:
- 支持Excel文件导入 (.xlsx, .xls)
- 支持指定Excel工作表导入
- 增强的字段映射和错误处理
- 自动文件格式检测
- 示例文件生成功能

使用方法:
    python import_free_templates.py data.csv                    # CSV导入
    python import_free_templates.py data.xlsx                   # Excel导入
    python import_free_templates.py data.xlsx --sheet "工作表"   # 指定工作表导入
    python import_free_templates.py --sample-excel              # 创建示例Excel文件
    python import_free_templates.py --list-sheets data.xlsx     # 列出工作表
    python import_free_templates.py --help                      # 显示帮助

依赖要求:
    pip install pandas>=2.0.0 openpyxl>=3.1.0
"""
import csv
import os
import sys
from sqlalchemy.orm import sessionmaker
import pandas as pd

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import engine
from app.models.user import FreeTemplate

def import_templates_from_csv(csv_file_path: str):
    """
    从CSV文件导入免费模板数据

    Args:
        csv_file_path: CSV文件路径
    """
    if not os.path.exists(csv_file_path):
        print(f"错误：CSV文件不存在: {csv_file_path}")
        return False

    # 创建数据库会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()

    try:
        # 读取CSV文件
        with open(csv_file_path, 'r', encoding='gbk') as file:
            # 尝试检测CSV格式
            sample = file.read(1024)
            file.seek(0)

            # 检测分隔符
            delimiter = ','
            if '\t' in sample:
                delimiter = '\t'
            elif ';' in sample:
                delimiter = ';'

            csv_reader = csv.DictReader(file, delimiter=delimiter)

            print(f"检测到的CSV列名: {csv_reader.fieldnames}")

            imported_count = 0
            updated_count = 0

            for row_num, row in enumerate(csv_reader, start=2):  # 从第2行开始（第1行是标题）
                try:
                    # 处理数据，支持不同的列名格式
                    template_data = {}

                    # 映射CSV列名到数据库字段名
                    field_mapping = {
                        'id': ['id', 'ID', '模板ID', 'template_id'],
                        'batch_flag': ['batch_flag', 'batchFlag', '批次', 'batch', '分类'],
                        'thumb_path': ['thumb_path', 'thumbPath', '缩略图路径', 'thumbnail', '图片路径'],
                        'baidu_url': ['baidu_url', 'baiduUrl', '百度链接', 'baidu_link', '百度网盘'],
                        'baidu_pass': ['baidu_pass', 'baiduPass', '百度提取码', 'baidu_password', '百度密码'],
                        'quark_url': ['quark_url', 'quarkUrl', '夸克链接', 'quark_link', '夸克网盘'],
                        'quark_pass': ['quark_pass', 'quarkPass', '夸克提取码', 'quark_password', '夸克密码'],
                        'download_count': ['download_count', 'downloadCount', '下载次数', 'downloads'],
                        'type': ['type', 'file_type', '文件类型', '类型'],
                        'sort_order': ['sort_order', 'sortOrder', '排序', '排序优先级', '显示顺序', 'order']
                    }

                    # 根据映射提取数据
                    for db_field, csv_fields in field_mapping.items():
                        for csv_field in csv_fields:
                            if csv_field in row and row[csv_field]:
                                template_data[db_field] = row[csv_field].strip()
                                break

                    # 验证必需字段
                    if not template_data.get('id'):
                        print(f"警告：第{row_num}行缺少模板ID，跳过")
                        continue

                    # 设置默认值
                    template_data.setdefault('batch_flag', template_data['id'].split('/')[0] if '/' in template_data['id'] else 'default')
                    template_data.setdefault('thumb_path', f"free_resume_templates/{template_data['batch_flag']}/{template_data['id']}")
                    template_data.setdefault('download_count', 0)
                    template_data.setdefault('type', 'word')
                    template_data.setdefault('sort_order', 0)

                    # 转换数据类型
                    try:
                        template_data['download_count'] = int(template_data.get('download_count', 0))
                    except (ValueError, TypeError):
                        template_data['download_count'] = 0

                    try:
                        template_data['sort_order'] = int(template_data.get('sort_order', 0))
                    except (ValueError, TypeError):
                        template_data['sort_order'] = 0

                    # 检查是否已存在
                    existing_template = session.query(FreeTemplate).filter(
                        FreeTemplate.id == template_data['id']
                    ).first()

                    if existing_template:
                        # 更新现有记录
                        for key, value in template_data.items():
                            if key != 'id':  # 不更新主键
                                setattr(existing_template, key, value)
                        updated_count += 1
                        print(f"更新模板: {template_data['id']}")
                    else:
                        # 创建新记录
                        new_template = FreeTemplate(**template_data)
                        session.add(new_template)
                        imported_count += 1
                        print(f"导入模板: {template_data['id']}")

                except Exception as e:
                    print(f"错误：处理第{row_num}行时出错: {str(e)}")
                    print(f"行数据: {row}")
                    continue

            # 提交事务
            session.commit()

            print(f"\n导入完成！")
            print(f"新增模板: {imported_count} 个")
            print(f"更新模板: {updated_count} 个")
            print(f"总计处理: {imported_count + updated_count} 个模板")

            return True

    except Exception as e:
        session.rollback()
        print(f"导入失败: {str(e)}")
        return False
    finally:
        session.close()

def import_templates_from_excel(excel_file_path: str, sheet_name: str = None):
    """
    从Excel文件导入免费模板数据

    Args:
        excel_file_path: Excel文件路径
        sheet_name: 工作表名称，如果为None则使用第一个工作表
    """
    if not os.path.exists(excel_file_path):
        print(f"错误：Excel文件不存在: {excel_file_path}")
        return False

    # 创建数据库会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_file_path}")

        # 获取所有工作表名称
        excel_file = pd.ExcelFile(excel_file_path)
        available_sheets = excel_file.sheet_names
        print(f"可用的工作表: {available_sheets}")

        # 确定要读取的工作表
        if sheet_name is None:
            target_sheet = available_sheets[0]
            print(f"未指定工作表，使用第一个工作表: {target_sheet}")
        elif sheet_name in available_sheets:
            target_sheet = sheet_name
            print(f"使用指定工作表: {target_sheet}")
        else:
            print(f"错误：指定的工作表 '{sheet_name}' 不存在")
            print(f"可用的工作表: {available_sheets}")
            return False

        # 读取指定工作表
        df = pd.read_excel(excel_file_path, sheet_name=target_sheet)

        # 显示列名
        print(f"检测到的Excel列名: {list(df.columns)}")
        print(f"数据行数: {len(df)}")

        # 字段映射配置
        field_mapping = {
            'id': ['id', 'ID', '模板ID', 'template_id', '编号'],
            'batch_flag': ['batch_flag', 'batchFlag', '批次', 'batch', '分类', '批次标识'],
            'thumb_path': ['thumb_path', 'thumbPath', '缩略图路径', 'thumbnail', '图片路径', '缩略图'],
            'baidu_url': ['baidu_url', 'baiduUrl', '百度链接', 'baidu_link', '百度网盘', '百度网盘链接'],
            'baidu_pass': ['baidu_pass', 'baiduPass', '百度提取码', 'baidu_password', '百度密码', '百度网盘提取码'],
            'quark_url': ['quark_url', 'quarkUrl', '夸克链接', 'quark_link', '夸克网盘', '夸克网盘链接'],
            'quark_pass': ['quark_pass', 'quarkPass', '夸克提取码', 'quark_password', '夸克密码', '夸克网盘提取码'],
            'download_count': ['download_count', 'downloadCount', '下载次数', 'downloads', '下载量'],
            'type': ['type', 'file_type', '文件类型', '类型', '格式'],
            'sort_order': ['sort_order', 'sortOrder', '排序', '排序优先级', '显示顺序', 'order']
        }

        imported_count = 0
        updated_count = 0
        error_count = 0

        # 处理每一行数据
        for index, row in df.iterrows():
            try:
                row_num = index + 2  # Excel行号从2开始（第1行是标题）

                # 处理数据，支持不同的列名格式
                template_data = {}

                # 根据映射提取数据
                for db_field, excel_fields in field_mapping.items():
                    for excel_field in excel_fields:
                        if excel_field in df.columns and pd.notna(row[excel_field]):
                            # 处理字符串类型的数据
                            value = str(row[excel_field]).strip() if row[excel_field] else ""
                            if value:  # 只有非空值才赋值
                                template_data[db_field] = value
                                break

                # 验证必需字段
                if not template_data.get('id'):
                    print(f"警告：第{row_num}行缺少模板ID，跳过")
                    error_count += 1
                    continue

                # 设置默认值
                template_data.setdefault('batch_flag', template_data['id'].split('/')[0] if '/' in template_data['id'] else 'default')
                template_data.setdefault('thumb_path', f"free_resume_templates/{template_data['batch_flag']}/{template_data['id']}")
                template_data.setdefault('download_count', 0)
                template_data.setdefault('type', 'word')
                template_data.setdefault('sort_order', 0)

                # 转换数据类型
                try:
                    template_data['download_count'] = int(float(template_data.get('download_count', 0)))
                except (ValueError, TypeError):
                    template_data['download_count'] = 0

                try:
                    template_data['sort_order'] = int(float(template_data.get('sort_order', 0)))
                except (ValueError, TypeError):
                    template_data['sort_order'] = 0

                # 检查是否已存在
                existing_template = session.query(FreeTemplate).filter(
                    FreeTemplate.id == template_data['id']
                ).first()

                if existing_template:
                    # 更新现有记录
                    for key, value in template_data.items():
                        if key != 'id':  # 不更新主键
                            setattr(existing_template, key, value)
                    updated_count += 1
                    print(f"更新模板: {template_data['id']}")
                else:
                    # 创建新记录
                    new_template = FreeTemplate(**template_data)
                    session.add(new_template)
                    imported_count += 1
                    print(f"导入模板: {template_data['id']}")

            except Exception as e:
                print(f"错误：处理第{row_num}行时出错: {str(e)}")
                print(f"行数据: {dict(row)}")
                error_count += 1
                continue

        # 提交事务
        session.commit()

        print(f"\n导入完成！")
        print(f"新增模板: {imported_count} 个")
        print(f"更新模板: {updated_count} 个")
        print(f"错误记录: {error_count} 个")
        print(f"总计处理: {imported_count + updated_count} 个模板")

        return True

    except Exception as e:
        session.rollback()
        print(f"导入失败: {str(e)}")
        return False
    finally:
        session.close()

def create_sample_csv():
    """创建示例CSV文件"""
    sample_data = [
        {
            'id': 'blackWhite/10.jpg',
            'batch_flag': 'blackWhite',
            'thumb_path': 'free_resume_templates/blackWhite/10.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567890',
            'baidu_pass': '00aa',
            'quark_url': 'https://sxsdlfkjslfjsdf',
            'quark_pass': '00aa',
            'download_count': '0',
            'type': 'word'
        },
        {
            'id': 'blackWhite/11.jpg',
            'batch_flag': 'blackWhite',
            'thumb_path': 'free_resume_templates/blackWhite/11.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567891',
            'baidu_pass': '00bb',
            'quark_url': 'https://sxsdlfkjslfjsdf1',
            'quark_pass': '00bb',
            'download_count': '5',
            'type': 'word'
        },
        {
            'id': 'colorful/01.jpg',
            'batch_flag': 'colorful',
            'thumb_path': 'free_resume_templates/colorful/01.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567892',
            'baidu_pass': '00cc',
            'quark_url': 'https://sxsdlfkjslfjsdf2',
            'quark_pass': '00cc',
            'download_count': '12',
            'type': 'word'
        }
    ]

    csv_file = 'sample_free_templates.csv'
    with open(csv_file, 'w', newline='', encoding='utf-8') as file:
        fieldnames = ['id', 'batch_flag', 'thumb_path', 'baidu_url', 'baidu_pass', 'quark_url', 'quark_pass', 'download_count', 'type']
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(sample_data)

    print(f"示例CSV文件已创建: {csv_file}")
    return csv_file

def create_sample_excel():
    """创建示例Excel文件"""
    sample_data = [
        {
            'id': 'blackWhite/10.jpg',
            'batch_flag': 'blackWhite',
            'thumb_path': 'free_resume_templates/blackWhite/10.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567890',
            'baidu_pass': '00aa',
            'quark_url': 'https://sxsdlfkjslfjsdf',
            'quark_pass': '00aa',
            'download_count': 0,
            'type': 'word'
        },
        {
            'id': 'blackWhite/11.jpg',
            'batch_flag': 'blackWhite',
            'thumb_path': 'free_resume_templates/blackWhite/11.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567891',
            'baidu_pass': '00bb',
            'quark_url': 'https://sxsdlfkjslfjsdf1',
            'quark_pass': '00bb',
            'download_count': 5,
            'type': 'word'
        },
        {
            'id': 'colorful/01.jpg',
            'batch_flag': 'colorful',
            'thumb_path': 'free_resume_templates/colorful/01.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567892',
            'baidu_pass': '00cc',
            'quark_url': 'https://sxsdlfkjslfjsdf2',
            'quark_pass': '00cc',
            'download_count': 12,
            'type': 'word'
        },
        {
            'id': 'modern/05.jpg',
            'batch_flag': 'modern',
            'thumb_path': 'free_resume_templates/modern/05.jpg',
            'baidu_url': 'https://pan.baidu.com/s/1234567893',
            'baidu_pass': '00dd',
            'quark_url': 'https://sxsdlfkjslfjsdf3',
            'quark_pass': '00dd',
            'download_count': 8,
            'type': 'word'
        }
    ]

    # 创建DataFrame
    df = pd.DataFrame(sample_data)

    # 创建Excel文件，包含多个工作表
    excel_file = 'sample_free_templates.xlsx'
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # 写入主工作表
        df.to_excel(writer, sheet_name='免费模板', index=False)

        # 创建分类工作表
        blackwhite_data = [item for item in sample_data if item['batch_flag'] == 'blackWhite']
        colorful_data = [item for item in sample_data if item['batch_flag'] == 'colorful']
        modern_data = [item for item in sample_data if item['batch_flag'] == 'modern']

        if blackwhite_data:
            pd.DataFrame(blackwhite_data).to_excel(writer, sheet_name='黑白模板', index=False)
        if colorful_data:
            pd.DataFrame(colorful_data).to_excel(writer, sheet_name='彩色模板', index=False)
        if modern_data:
            pd.DataFrame(modern_data).to_excel(writer, sheet_name='现代模板', index=False)

    print(f"示例Excel文件已创建: {excel_file}")
    print("包含的工作表:")
    print("- 免费模板 (所有数据)")
    print("- 黑白模板 (blackWhite分类)")
    print("- 彩色模板 (colorful分类)")
    print("- 现代模板 (modern分类)")
    return excel_file

def list_excel_sheets(excel_file_path: str):
    """列出Excel文件中的所有工作表"""
    if not os.path.exists(excel_file_path):
        print(f"错误：Excel文件不存在: {excel_file_path}")
        return False

    try:
        excel_file = pd.ExcelFile(excel_file_path)
        sheets = excel_file.sheet_names
        print(f"Excel文件 '{excel_file_path}' 包含的工作表:")
        for i, sheet in enumerate(sheets, 1):
            print(f"  {i}. {sheet}")
        return sheets
    except Exception as e:
        print(f"读取Excel文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("免费简历模板数据导入工具")
        print("\n用法:")
        print("  python import_free_templates.py <file_path>                    # 从CSV/Excel文件导入")
        print("  python import_free_templates.py <excel_file> --sheet <name>    # 从Excel指定工作表导入")
        print("  python import_free_templates.py --sample-csv                   # 创建示例CSV文件")
        print("  python import_free_templates.py --sample-excel                 # 创建示例Excel文件")
        print("  python import_free_templates.py --list-sheets <excel_file>     # 列出Excel工作表")
        print("  python import_free_templates.py --help                         # 显示帮助")
        return

    # 处理特殊命令
    if sys.argv[1] == '--sample-csv':
        csv_file = create_sample_csv()
        print(f"\n可以使用以下命令导入示例数据:")
        print(f"python import_free_templates.py {csv_file}")

    elif sys.argv[1] == '--sample-excel':
        excel_file = create_sample_excel()
        print(f"\n可以使用以下命令导入示例数据:")
        print(f"python import_free_templates.py {excel_file}")
        print(f"python import_free_templates.py {excel_file} --sheet 黑白模板")

    elif sys.argv[1] == '--list-sheets':
        if len(sys.argv) < 3:
            print("错误：请指定Excel文件路径")
            print("用法: python import_free_templates.py --list-sheets <excel_file>")
            return
        list_excel_sheets(sys.argv[2])

    elif sys.argv[1] == '--help':
        print("免费简历模板数据导入工具")
        print("\n功能说明:")
        print("- 支持CSV和Excel文件导入")
        print("- Excel文件支持指定工作表导入")
        print("- 自动检测文件格式")
        print("- 支持多种列名格式")
        print("\n支持的列名格式:")
        print("- id/ID/模板ID/template_id/编号: 模板ID")
        print("- batch_flag/批次/batch/分类/批次标识: 批次标识")
        print("- thumb_path/缩略图路径/thumbnail/图片路径/缩略图: 缩略图路径")
        print("- baidu_url/百度链接/baidu_link/百度网盘/百度网盘链接: 百度网盘链接")
        print("- baidu_pass/百度提取码/baidu_password/百度密码/百度网盘提取码: 百度网盘提取码")
        print("- quark_url/夸克链接/quark_link/夸克网盘/夸克网盘链接: 夸克网盘链接")
        print("- quark_pass/夸克提取码/quark_password/夸克密码/夸克网盘提取码: 夸克网盘提取码")
        print("- download_count/下载次数/downloads/下载量: 下载次数")
        print("- type/file_type/文件类型/类型/格式: 文件类型")

    else:
        # 处理文件导入
        file_path = sys.argv[1]
        sheet_name = None

        # 检查是否指定了工作表
        if len(sys.argv) >= 4 and sys.argv[2] == '--sheet':
            sheet_name = sys.argv[3]

        # 检查文件是否存在
        if not os.path.exists(file_path):
            print(f"错误：文件不存在: {file_path}")
            return

        # 根据文件扩展名判断文件类型
        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext in ['.xlsx', '.xls']:
            # Excel文件
            print(f"开始从Excel文件导入数据: {file_path}")
            if sheet_name:
                print(f"指定工作表: {sheet_name}")
            success = import_templates_from_excel(file_path, sheet_name)
        elif file_ext == '.csv':
            # CSV文件
            if sheet_name:
                print("警告：CSV文件不支持工作表参数，将忽略 --sheet 参数")
            print(f"开始从CSV文件导入数据: {file_path}")
            success = import_templates_from_csv(file_path)
        else:
            print(f"错误：不支持的文件格式: {file_ext}")
            print("支持的格式: .csv, .xlsx, .xls")
            return

        if success:
            print("导入成功！")
        else:
            print("导入失败！")
            sys.exit(1)

if __name__ == "__main__":
    main()
