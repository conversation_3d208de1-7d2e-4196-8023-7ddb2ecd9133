#!/usr/bin/env python3
"""
创建包含sort_order字段的测试数据
"""
import pandas as pd

def create_test_data():
    """创建测试数据"""
    sample_data = [
        {
            'id': 'featured/hot01.jpg',
            'batch_flag': 'featured',
            'sort_order': 1,
            'thumb_path': 'free_resume_templates/featured/hot01.jpg',
            'baidu_url': 'https://pan.baidu.com/s/test001',
            'baidu_pass': 'test',
            'quark_url': 'https://pan.quark.cn/s/test001',
            'quark_pass': 'test',
            'download_count': 100,
            'type': 'word'
        },
        {
            'id': 'featured/hot02.jpg',
            'batch_flag': 'featured',
            'sort_order': 2,
            'thumb_path': 'free_resume_templates/featured/hot02.jpg',
            'baidu_url': 'https://pan.baidu.com/s/test002',
            'baidu_pass': 'test',
            'quark_url': 'https://pan.quark.cn/s/test002',
            'quark_pass': 'test',
            'download_count': 80,
            'type': 'word'
        },
        {
            'id': 'featured/hot03.jpg',
            'batch_flag': 'featured',
            'sort_order': 3,
            'thumb_path': 'free_resume_templates/featured/hot03.jpg',
            'baidu_url': 'https://pan.baidu.com/s/test003',
            'baidu_pass': 'test',
            'quark_url': 'https://pan.quark.cn/s/test003',
            'quark_pass': 'test',
            'download_count': 60,
            'type': 'word'
        }
    ]
    
    # 创建DataFrame并保存为Excel
    df = pd.DataFrame(sample_data)
    df.to_excel('test_sort_order.xlsx', index=False)
    print('创建测试文件: test_sort_order.xlsx')
    print('包含字段:', list(df.columns))
    
    # 也创建CSV版本
    df.to_csv('test_sort_order.csv', index=False)
    print('创建测试文件: test_sort_order.csv')

if __name__ == "__main__":
    create_test_data()
