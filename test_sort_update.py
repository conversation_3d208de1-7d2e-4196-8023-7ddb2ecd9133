#!/usr/bin/env python3
"""
测试排序功能的脚本
"""
import mysql.connector
from mysql.connector import Error
import requests
import json

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'database': 'resume_service',
    'user': 'resume_user',
    'password': 'Resume123!'
}

def update_sort_order():
    """更新一些模板的排序值进行测试"""
    connection = None
    cursor = None
    
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        print("=== 更新排序值测试 ===")
        
        # 获取前几个模板的ID
        cursor.execute("SELECT id FROM free_templates LIMIT 5")
        template_ids = cursor.fetchall()
        
        # 更新前3个模板的排序值
        for i, (template_id,) in enumerate(template_ids[:3]):
            new_sort_order = -(i + 1)  # -1, -2, -3
            cursor.execute("UPDATE free_templates SET sort_order = %s WHERE id = %s", 
                         (new_sort_order, template_id))
            print(f"设置模板 {template_id} 的sort_order为 {new_sort_order}")
        
        connection.commit()
        print("排序值更新完成！")
        
        # 验证更新结果
        cursor.execute("SELECT id, sort_order FROM free_templates ORDER BY sort_order ASC LIMIT 5")
        results = cursor.fetchall()
        
        print("\n更新后的排序结果:")
        for template_id, sort_order in results:
            print(f"  {template_id}: sort_order = {sort_order}")
            
        return True
        
    except Error as e:
        print(f"数据库操作错误: {e}")
        if connection:
            connection.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if connection and connection.is_connected():
            connection.close()

def test_api_sorting():
    """测试API排序功能"""
    print("\n=== 测试API排序功能 ===")
    
    try:
        # 测试默认排序（sort_order升序）
        response = requests.get('http://localhost:18080/free-templates/?limit=3')
        if response.status_code == 200:
            data = response.json()
            print("默认排序（sort_order升序）前3个模板:")
            for i, template in enumerate(data['templates'], 1):
                print(f"  {i}. {template['id']}")
        
        print()
        
        # 测试按下载次数降序排序
        response = requests.get('http://localhost:18080/free-templates/?order_by=download_count&order_direction=desc&limit=3')
        if response.status_code == 200:
            data = response.json()
            print("按下载次数降序排序前3个模板:")
            for i, template in enumerate(data['templates'], 1):
                print(f"  {i}. {template['id']}")
        
        print()
        
        # 测试按创建时间降序排序
        response = requests.get('http://localhost:18080/free-templates/?order_by=created_at&order_direction=desc&limit=3')
        if response.status_code == 200:
            data = response.json()
            print("按创建时间降序排序前3个模板:")
            for i, template in enumerate(data['templates'], 1):
                print(f"  {i}. {template['id']}")
                
    except Exception as e:
        print(f"API测试错误: {e}")

def main():
    """主函数"""
    print("开始测试排序功能...")
    
    # 更新排序值
    if update_sort_order():
        # 测试API
        test_api_sorting()
        print("\n排序功能测试完成！")
    else:
        print("排序值更新失败！")

if __name__ == "__main__":
    main()
